# 数据库事务和协程安全改进

## 概述

本文档记录了对支付后端服务数据库操作的协程安全性和事务管理的改进。

## 改进内容

### 1. 协程安全问题修复

#### 雪花算法节点初始化
**问题：** 全局变量 `snowflakeNode` 存在竞态条件，多个 goroutine 可能同时初始化。

**修复前：**
```go
var snowflakeNode *snowflake.Node

func GenerateOrderID(amount int64, paymentMethod string) string {
    if snowflakeNode == nil {
        InitSnowflake(0) // 竞态条件！
    }
    // ...
}
```

**修复后：**
```go
var (
    snowflakeNode *snowflake.Node
    snowflakeOnce sync.Once
)

func InitSnowflake(nodeID int64) error {
    var err error
    snowflakeOnce.Do(func() {
        snowflakeNode, err = snowflake.NewNode(nodeID)
    })
    return err
}
```

**改进效果：**
- 使用 `sync.Once` 确保雪花算法节点只初始化一次
- 消除了并发初始化的竞态条件
- 保证了 ID 生成的协程安全性

### 2. 服务层事务管理

#### 订单创建事务保护
**问题：** 订单创建和支付会话创建之间缺乏事务保护，可能导致数据不一致。

**修复前：**
```go
func (s *orderService) CreateOrder(userCtx *middleware.UserContext, req *domain.CreateOrderRequest) (*domain.CreateOrderResponse, error) {
    // 保存订单到数据库
    if err := s.orderRepo.Create(order); err != nil {
        return nil, err
    }
    
    // 创建支付会话 - 如果失败，订单已经创建但无法回滚！
    checkoutURL, err := gateway.CreateCheckoutSession(order, req.SuccessURL, req.CancelURL)
    if err != nil {
        return nil, err
    }
}
```

**修复后：**
```go
func (s *orderService) CreateOrder(userCtx *middleware.UserContext, req *domain.CreateOrderRequest) (*domain.CreateOrderResponse, error) {
    var checkoutURL string
    var order *domain.Order

    err := db.WithTransaction(func(tx *gorm.DB) error {
        // 在事务中创建订单
        order = domain.NewOrder(userCtx.UserID, req)
        if err := s.orderRepo.CreateWithTransaction(tx, order); err != nil {
            return err
        }

        // 创建支付会话（如果失败会回滚订单创建）
        gateway, exists := s.gateways[req.PayedMethod]
        if !exists {
            return fmt.Errorf("payment gateway %s not supported", req.PayedMethod)
        }

        var err error
        checkoutURL, err = gateway.CreateCheckoutSession(order, req.SuccessURL, req.CancelURL)
        return err
    })

    if err != nil {
        return nil, err
    }
    // ...
}
```

#### 订单更新事务保护
**问题：** 获取和更新订单之间存在时间窗口，可能导致丢失更新问题。

**修复前：**
```go
func (s *orderService) UpdateOrder(orderID string, req *domain.UpdateOrderRequest) error {
    // 获取现有订单
    order, err := s.orderRepo.GetByOrderID(orderID)
    if err != nil {
        return err
    }
    
    // 更新字段...
    
    // 保存更新 - 在获取和更新之间，其他 goroutine 可能已经修改了订单
    if err := s.orderRepo.Update(order); err != nil {
        return err
    }
}
```

**修复后：**
```go
func (s *orderService) UpdateOrder(orderID string, req *domain.UpdateOrderRequest) error {
    err := db.WithTransaction(func(tx *gorm.DB) error {
        // 在事务中获取现有订单（使用行锁防止并发修改）
        order, err := s.orderRepo.GetByOrderIDWithTransaction(tx, orderID)
        if err != nil {
            return err
        }

        // 更新字段...

        // 在事务中保存更新
        return s.orderRepo.UpdateWithTransaction(tx, order)
    })
    // ...
}
```

#### 订单取消和退款事务保护
类似地，`CancelOrder` 和 `RefundOrder` 方法也被更新为使用事务保护。

### 3. 仓储层事务方法扩展

#### 接口扩展
在 `domain.OrderRepository` 接口中添加了事务版本的方法：

```go
type OrderRepository interface {
    // 原有方法...
    
    // 事务方法
    CreateWithTransaction(tx any, order *Order) error
    GetByIDWithTransaction(tx any, id uint64) (*Order, error)
    GetByOrderIDWithTransaction(tx any, orderID string) (*Order, error)
    UpdateWithTransaction(tx any, order *Order) error
    UpdateStatusWithTransaction(tx any, orderID string, payStatus string) error
    SoftDeleteWithTransaction(tx any, orderID string) error
}
```

#### MySQL 实现
在 `mysql.orderRepository` 中实现了所有事务方法，包含类型检查：

```go
func (r *orderRepository) CreateWithTransaction(tx any, order *domain.Order) error {
    gormTx, ok := tx.(*gorm.DB)
    if !ok {
        return fmt.Errorf("invalid transaction type")
    }
    // 使用 gormTx 进行数据库操作...
}
```

## 改进效果

### 协程安全性
- ✅ **GORM 连接池**：自动管理并发连接，协程安全
- ✅ **仓储层**：无状态设计，可安全并发使用
- ✅ **雪花算法**：使用 `sync.Once` 确保单次初始化
- ✅ **事务隔离**：每个事务在独立的 goroutine 中执行

### 数据一致性
- ✅ **原子操作**：相关操作在同一事务中执行
- ✅ **回滚机制**：任何步骤失败都会回滚整个事务
- ✅ **行锁保护**：防止并发修改导致的数据不一致
- ✅ **ACID 保证**：完整的事务 ACID 特性支持

### 性能优化
- ✅ **连接池配置**：合理的连接池参数设置
- ✅ **事务范围**：最小化事务持有时间
- ✅ **索引优化**：适当的数据库索引策略

## 测试验证

创建了单元测试来验证：
- 服务层方法的正确性
- 雪花算法初始化的协程安全性
- 模拟仓储的事务方法调用

## 最佳实践建议

1. **事务使用原则**
   - 保持事务尽可能短
   - 避免在事务中进行长时间的外部调用
   - 使用适当的事务隔离级别

2. **协程安全**
   - 避免共享可变状态
   - 使用适当的同步原语（如 `sync.Once`、`sync.Mutex`）
   - 依赖框架提供的协程安全保证

3. **错误处理**
   - 明确区分可重试和不可重试的错误
   - 提供详细的错误信息用于调试
   - 记录关键操作的日志

## 后续改进建议

1. **监控和指标**
   - 添加事务执行时间监控
   - 监控数据库连接池状态
   - 记录事务回滚频率

2. **性能优化**
   - 根据实际负载调整连接池参数
   - 考虑读写分离场景
   - 优化查询性能

3. **容错机制**
   - 实现重试机制
   - 添加熔断器模式
   - 考虑分布式事务场景
