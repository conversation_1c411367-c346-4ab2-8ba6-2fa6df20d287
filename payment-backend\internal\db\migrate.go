package db

import (
	"fmt"

	"gorm.io/gorm"
)

// Migrate 执行数据库迁移
func Migrate(db *gorm.DB) error {
	// 自动迁移表结构
	if err := AutoMigrate(db); err != nil {
		return fmt.Errorf("failed to auto migrate: %w", err)
	}

	// 创建索引
	if err := createIndexes(db); err != nil {
		return fmt.Errorf("failed to create indexes: %w", err)
	}

	return nil
}

// createIndexes 创建额外的索引
func createIndexes(db *gorm.DB) error {
	// 为orders表创建索引
	indexes := []string{
		// 订单ID唯一索引（已在模型中定义为uniqueIndex）
		"CREATE UNIQUE INDEX IF NOT EXISTS idx_orders_order_id ON orders(order_id)",

		// 用户ID索引，用于查询用户的订单
		"CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id)",

		// 支付状态索引，用于按状态查询订单
		"CREATE INDEX IF NOT EXISTS idx_orders_pay_status ON orders(pay_status)",

		// PSP支付ID索引，用于webhook回调查询
		"CREATE INDEX IF NOT EXISTS idx_orders_psp_payment_id ON orders(psp_payment_id)",

		// PSP客户ID索引，用于客户相关查询
		"CREATE INDEX IF NOT EXISTS idx_orders_psp_customer_id ON orders(psp_customer_id)",

		// 创建时间索引，用于按时间排序
		"CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at DESC)",

		// 用户ID和支付状态的复合索引，用于查询用户的特定状态订单
		"CREATE INDEX IF NOT EXISTS idx_orders_user_pay_status ON orders(user_id, pay_status)",

		// 用户ID和创建时间的复合索引，用于按时间排序查询用户订单
		"CREATE INDEX IF NOT EXISTS idx_orders_user_created ON orders(user_id, created_at DESC)",

		// 支付状态和创建时间的复合索引，用于按状态和时间查询
		"CREATE INDEX IF NOT EXISTS idx_orders_status_created ON orders(pay_status, created_at DESC)",

		// 软删除相关索引
		"CREATE INDEX IF NOT EXISTS idx_orders_deleted ON orders(deleted)",

		// 产品ID索引，用于产品相关统计
		"CREATE INDEX IF NOT EXISTS idx_orders_product_id ON orders(product_id)",

		// 价格ID索引，用于价格相关统计
		"CREATE INDEX IF NOT EXISTS idx_orders_price_id ON orders(price_id)",
	}

	for _, indexSQL := range indexes {
		if err := db.Exec(indexSQL).Error; err != nil {
			return fmt.Errorf("failed to create index: %s, error: %w", indexSQL, err)
		}
	}

	return nil
}

// DropTables 删除所有表（用于测试或重置）
func DropTables(db *gorm.DB) error {
	// 删除订单表
	tables := []string{
		"orders",
	}

	for _, table := range tables {
		if err := db.Exec(fmt.Sprintf("DROP TABLE IF EXISTS %s", table)).Error; err != nil {
			return fmt.Errorf("failed to drop table %s: %w", table, err)
		}
	}

	return nil
}

// CreateDatabase 创建数据库（如果不存在）
func CreateDatabase(db *gorm.DB, dbName string) error {
	sql := fmt.Sprintf("CREATE DATABASE IF NOT EXISTS %s CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci", dbName)
	return db.Exec(sql).Error
}

// CheckConnection 检查数据库连接
func CheckConnection(db *gorm.DB) error {
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	return sqlDB.Ping()
}

// GetTableInfo 获取表信息（用于调试）
func GetTableInfo(db *gorm.DB, tableName string) ([]map[string]interface{}, error) {
	var results []map[string]interface{}

	sql := fmt.Sprintf("DESCRIBE %s", tableName)
	if err := db.Raw(sql).Scan(&results).Error; err != nil {
		return nil, fmt.Errorf("failed to get table info for %s: %w", tableName, err)
	}

	return results, nil
}

// GetIndexInfo 获取索引信息（用于调试）
func GetIndexInfo(db *gorm.DB, tableName string) ([]map[string]interface{}, error) {
	var results []map[string]interface{}

	sql := fmt.Sprintf("SHOW INDEX FROM %s", tableName)
	if err := db.Raw(sql).Scan(&results).Error; err != nil {
		return nil, fmt.Errorf("failed to get index info for %s: %w", tableName, err)
	}

	return results, nil
}
