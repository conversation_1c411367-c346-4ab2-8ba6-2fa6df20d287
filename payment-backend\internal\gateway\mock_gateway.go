package gateway

import (
	"fmt"
	"net/url"

	"go.uber.org/zap"

	"payment-backend/internal/domain"
	"payment-backend/internal/logger"
)

// mockPaymentGateway 模拟支付网关实现
type mockPaymentGateway struct {
	provider string
	logger   logger.Logger
}

// NewMockPaymentGateway 创建模拟支付网关
func NewMockPaymentGateway(provider string, logger logger.Logger) domain.PaymentGateway {
	return &mockPaymentGateway{
		provider: provider,
		logger:   logger,
	}
}

// CreateCheckoutSession 创建结账会话
func (g *mockPaymentGateway) CreateCheckoutSession(order *domain.Order, successURL, cancelURL string) (string, error) {
	g.logger.Info("Creating mock checkout session",
		zap.String("provider", g.provider),
		zap.String("order_id", order.OrderID),
		zap.Float64("amount", order.Amount),
	)

	// 模拟生成checkout URL
	baseURL := "https://mock-payment.example.com"
	checkoutURL := fmt.Sprintf("%s/%s/checkout/%s", baseURL, g.provider, order.OrderID)

	// 添加查询参数
	u, err := url.Parse(checkoutURL)
	if err != nil {
		return "", fmt.Errorf("failed to parse checkout URL: %w", err)
	}

	q := u.Query()
	q.Set("amount", fmt.Sprintf("%.2f", order.Amount))
	q.Set("currency", order.Currency)
	q.Set("user_id", order.UserID)
	q.Set("success_url", successURL)
	q.Set("cancel_url", cancelURL)
	u.RawQuery = q.Encode()

	checkoutURL = u.String()

	g.logger.Info("Mock checkout session created",
		zap.String("provider", g.provider),
		zap.String("checkout_url", checkoutURL),
	)

	return checkoutURL, nil
}

// GetPaymentStatus 获取支付状态
func (g *mockPaymentGateway) GetPaymentStatus(pspPaymentID string) (string, error) {
	g.logger.Info("Getting mock payment status",
		zap.String("provider", g.provider),
		zap.String("psp_payment_id", pspPaymentID),
	)

	// 模拟返回支付状态
	// 在真实实现中，这里会调用支付提供商的API
	return domain.PayStatusSucceeded, nil
}

// RefundPayment 退款
func (g *mockPaymentGateway) RefundPayment(pspPaymentID string, amount float64) error {
	g.logger.Info("Processing mock refund",
		zap.String("provider", g.provider),
		zap.String("psp_payment_id", pspPaymentID),
		zap.Float64("amount", amount),
	)

	// 模拟退款处理
	// 在真实实现中，这里会调用支付提供商的退款API
	return nil
}

// VerifyWebhook 验证Webhook
func (g *mockPaymentGateway) VerifyWebhook(payload []byte, signature string) error {
	g.logger.Info("Verifying mock webhook",
		zap.String("provider", g.provider),
		zap.String("signature", signature),
		zap.Int("payload_size", len(payload)),
	)

	// 模拟webhook验证
	// 在真实实现中，这里会验证webhook签名
	if signature == "" {
		return fmt.Errorf("missing webhook signature")
	}

	return nil
}
