package main

import (
	"flag"
	"fmt"
	"log"
	"os"

	"payment-backend/internal/config"
	"payment-backend/internal/db"
)

func main() {
	var configPath = flag.String("config", "", "配置文件路径")
	var action = flag.String("action", "migrate", "操作类型: migrate, drop, info")
	flag.Parse()

	// 加载配置
	cfg, err := config.LoadConfig(*configPath)
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化数据库连接
	if err := db.Init(&cfg.Database); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	database := db.GetDB()

	switch *action {
	case "migrate":
		fmt.Println("Running database migration...")
		if err := db.Migrate(database); err != nil {
			log.Fatalf("Failed to migrate database: %v", err)
		}
		fmt.Println("Database migration completed successfully")

	case "drop":
		fmt.Println("Dropping all tables...")
		if err := db.DropTables(database); err != nil {
			log.Fatalf("Failed to drop tables: %v", err)
		}
		fmt.Println("All tables dropped successfully")

	case "info":
		fmt.Println("Database connection info:")
		fmt.Printf("Driver: %s\n", cfg.Database.Driver)
		fmt.Printf("Host: %s\n", cfg.Database.Host)
		fmt.Printf("Port: %d\n", cfg.Database.Port)
		fmt.Printf("Database: %s\n", cfg.Database.Database)
		fmt.Printf("Username: %s\n", cfg.Database.Username)

		// 测试连接
		if err := db.CheckConnection(database); err != nil {
			log.Fatalf("Database connection failed: %v", err)
		}
		fmt.Println("Database connection successful")

		// 获取表信息
		tables := []string{"payments", "checkout_sessions", "checkout_session_items"}
		for _, table := range tables {
			fmt.Printf("\nTable: %s\n", table)
			info, err := db.GetTableInfo(database, table)
			if err != nil {
				fmt.Printf("  Error: %v\n", err)
				continue
			}
			for _, column := range info {
				fmt.Printf("  %v\n", column)
			}
		}

	default:
		fmt.Printf("Unknown action: %s\n", *action)
		fmt.Println("Available actions: migrate, drop, info")
		os.Exit(1)
	}
}
