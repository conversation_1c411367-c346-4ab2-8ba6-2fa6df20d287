package app

import (
	"go.uber.org/fx"
	"gorm.io/gorm"

	"payment-backend/internal/config"
	"payment-backend/internal/db"
	"payment-backend/internal/domain"
	"payment-backend/internal/gateway"
	"payment-backend/internal/handler"
	"payment-backend/internal/logger"
	"payment-backend/internal/repository/mysql"
	"payment-backend/internal/server"
	"payment-backend/internal/service"
)

// ConfigModule 配置模块
var ConfigModule = fx.Module("config",
	fx.Provide(func() (*config.Config, error) {
		return config.LoadConfig("")
	}),
)

// LoggerModule 日志模块
var LoggerModule = fx.Module("logger",
	fx.Provide(func(cfg *config.Config) (logger.Logger, error) {
		return logger.NewLogger(&cfg.Log)
	}),
)

// DatabaseModule 数据库模块
var DatabaseModule = fx.Module("database",
	fx.Provide(func(cfg *config.Config) (*gorm.DB, error) {
		// 初始化数据库连接
		if err := db.Init(&cfg.Database); err != nil {
			return nil, err
		}

		// 执行数据库迁移
		if err := db.Migrate(db.GetDB()); err != nil {
			return nil, err
		}

		return db.GetDB(), nil
	}),
)

// RepositoryModule 仓储模块
var RepositoryModule = fx.Module("repository",
	fx.Provide(
		fx.Annotate(
			mysql.NewOrderRepository,
			fx.As(new(domain.OrderRepository)),
		),
	),
)

// GatewayModule 网关模块
var GatewayModule = fx.Module("gateway",
	fx.Provide(
		func(logger logger.Logger) map[string]domain.PaymentGateway {
			return map[string]domain.PaymentGateway{
				"paypal": gateway.NewMockPaymentGateway("paypal", logger),
				"stripe": gateway.NewMockPaymentGateway("stripe", logger),
			}
		},
	),
)

// ServiceModule 服务模块
var ServiceModule = fx.Module("service",
	fx.Provide(
		fx.Annotate(
			func(orderRepo domain.OrderRepository, gateways map[string]domain.PaymentGateway, cfg *config.Config, logger logger.Logger) domain.OrderService {
				return service.NewOrderService(orderRepo, gateways, cfg, logger)
			},
			fx.As(new(domain.OrderService)),
		),
	),
)

// HandlerModule 处理器模块
var HandlerModule = fx.Module("handler",
	fx.Provide(handler.NewOrderHandler),
)

// AppModule 应用模块
var AppModule = fx.Module("app",
	ConfigModule,
	LoggerModule,
	DatabaseModule,
	RepositoryModule,
	GatewayModule,
	ServiceModule,
	HandlerModule,
	server.ServerModule,
)

// NewApp 创建应用
func NewApp() *fx.App {
	return fx.New(
		AppModule,
		fx.NopLogger, // 禁用fx的默认日志，使用我们自己的日志
	)
}
